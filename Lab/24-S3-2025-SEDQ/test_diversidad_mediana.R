# Script para probar la diversidad del ejercicio de mediana
# Carga de la librería r-exams
library(exams)

# Configuración del ejercicio
archivo_examen <- "mediana_salas_cine_formulacion_ejecucion_v2.Rmd"
copias <- 10  # Generar 10 versiones diferentes
numpreg <- 1  # Una pregunta por versión
dir_salida <- "salida_test_diversidad"
dir_ejercicios <- "."

# Crear directorio de salida si no existe
if (!dir.exists(dir_salida)) {
  dir.create(dir_salida)
}

# Nombre del archivo sin la extensión .Rmd
nombre_sin_extension <- sub("\\.Rmd$", "", archivo_examen)

cat("=== GENERANDO", copias, "VERSIONES DIFERENTES DEL EJERCICIO DE MEDIANA ===\n")

################################################################################
# Generación de copias individuales para HTML (para verificar diversidad)

for(i in 1:copias) {
  # IMPORTANTE: NO establecer semilla fija para permitir aleatorización
  # Cada iteración debe generar una versión diferente
  
  nombre_archivo <- sprintf("%s_version%d", nombre_sin_extension, i)
  
  cat("Generando versión", i, "...\n")
  
  tryCatch({
    exams2html(archivo_examen,
               n = 1,
               name = nombre_archivo,
               encoding = "UTF-8",
               dir = dir_salida,
               edir = dir_ejercicios,
               verbose = FALSE)  # Reducir verbosidad para claridad
    
    cat("✅ Versión", i, "generada exitosamente\n")
    
  }, error = function(e) {
    cat("❌ Error en versión", i, ":", e$message, "\n")
  })
}

################################################################################
# Verificación de diversidad comparando archivos HTML generados

cat("\n=== VERIFICANDO DIVERSIDAD DE VERSIONES ===\n")

archivos_html <- list.files(dir_salida, pattern = paste0(nombre_sin_extension, "_version.*\\.html"), full.names = TRUE)

if (length(archivos_html) >= 2) {
  # Leer contenido de los primeros archivos para comparar
  contenidos <- list()
  for (i in 1:min(5, length(archivos_html))) {
    contenidos[[i]] <- readLines(archivos_html[i], warn = FALSE)
  }
  
  # Comparar contenidos
  versiones_diferentes <- 0
  for (i in 2:length(contenidos)) {
    if (!identical(contenidos[[1]], contenidos[[i]])) {
      versiones_diferentes <- versiones_diferentes + 1
    }
  }
  
  cat("📊 Archivos HTML generados:", length(archivos_html), "\n")
  cat("📊 Versiones diferentes detectadas:", versiones_diferentes, "de", length(contenidos) - 1, "comparaciones\n")
  
  if (versiones_diferentes > 0) {
    cat("✅ DIVERSIDAD CONFIRMADA: Las versiones son diferentes\n")
  } else {
    cat("❌ PROBLEMA DE DIVERSIDAD: Las versiones son idénticas\n")
  }
  
} else {
  cat("⚠️ No se pudieron generar suficientes archivos para comparar\n")
}

################################################################################
# Generación de un PDF con múltiples versiones para verificación visual

cat("\n=== GENERANDO PDF CON MÚLTIPLES VERSIONES ===\n")

tryCatch({
  # Generar PDF con múltiples versiones
  exams2pdf(archivo_examen,
            n = 5,  # 5 versiones en un solo PDF
            name = paste0(nombre_sin_extension, "_multiples_versiones"),
            encoding = "UTF-8",
            template = "plain",
            dir = dir_salida,
            edir = dir_ejercicios,
            verbose = TRUE)
  
  cat("✅ PDF con múltiples versiones generado exitosamente\n")
  
}, error = function(e) {
  cat("❌ Error generando PDF:", e$message, "\n")
})

cat("\n=== PRUEBA DE DIVERSIDAD COMPLETADA ===\n")
cat("Revisa los archivos en el directorio:", dir_salida, "\n")
cat("- Archivos HTML individuales para comparación detallada\n")
cat("- PDF con múltiples versiones para verificación visual\n")
