---
output:
  html_document: default
  word_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies:
      - "graphicx"
      - "float"
      - "tikz"
      - "xcolor"
      - "booktabs"
      - "array"

# Metadatos ICFES
icfes:
  competencia: formulacion_ejecucion
  nivel_dificultad: 2
  contenido:
    categoria: estadistica
    tipo: generico
  contexto: familiar
  eje_axial: eje3
  componente: aleatorio
---

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}",
  "\\usepackage{booktabs}",
  "\\usepackage{array}",
  "\\usepackage{xcolor}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r generar_datos, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Función principal para generar datos de mediana en contexto de salas de cine
generar_datos <- function() {
  # Aleatorización de contextos de salas de cine (mínimo 8-10 escenarios)
  contextos_cine <- list(
    list(nombre_cine = "Cineplex Central", tipo_sala = "salas VIP", genero = "acción"),
    list(nombre_cine = "Multiplex Norte", tipo_sala = "salas estándar", genero = "comedia"),
    list(nombre_cine = "CineMax Plaza", tipo_sala = "salas IMAX", genero = "ciencia ficción"),
    list(nombre_cine = "Cine Familiar", tipo_sala = "salas tradicionales", genero = "animación"),
    list(nombre_cine = "Megaplex Sur", tipo_sala = "salas premium", genero = "drama"),
    list(nombre_cine = "Cine Boutique", tipo_sala = "salas boutique", genero = "thriller"),
    list(nombre_cine = "Cinema City", tipo_sala = "salas digitales", genero = "aventura"),
    list(nombre_cine = "Cine Retro", tipo_sala = "salas clásicas", genero = "romance"),
    list(nombre_cine = "UltraCine", tipo_sala = "salas 4DX", genero = "fantasía"),
    list(nombre_cine = "Cine Indie", tipo_sala = "salas independientes", genero = "documental")
  )
  
  contexto_seleccionado <- sample(contextos_cine, 1)[[1]]
  
  # Generar número de salas (5-15 salas para cálculo de mediana)
  num_salas <- sample(5:15, 1)
  
  # Generar datos de asistencia (rangos 200-600 asistentes)
  # Asegurar variabilidad y evitar valores duplicados problemáticos
  asistencia_base <- sample(200:600, num_salas, replace = FALSE)
  
  # Añadir pequeña variación aleatoria para mayor diversidad
  variacion <- sample(-10:10, num_salas, replace = TRUE)
  asistencia_final <- pmax(200, pmin(600, asistencia_base + variacion))
  
  # Generar nombres de salas aleatorios
  letras_salas <- LETTERS[1:num_salas]
  
  # Calcular mediana correcta
  datos_ordenados <- sort(asistencia_final)
  n <- length(datos_ordenados)
  
  if (n %% 2 == 1) {
    # Número impar de datos
    mediana_correcta <- datos_ordenados[(n + 1) / 2]
  } else {
    # Número par de datos
    pos_central1 <- n / 2
    pos_central2 <- pos_central1 + 1
    mediana_correcta <- (datos_ordenados[pos_central1] + datos_ordenados[pos_central2]) / 2
  }
  
  return(list(
    contexto = contexto_seleccionado,
    num_salas = num_salas,
    letras_salas = letras_salas,
    asistencia = asistencia_final,
    datos_ordenados = datos_ordenados,
    mediana_correcta = mediana_correcta,
    es_par = (n %% 2 == 0)
  ))
}

# Generar datos para este ejercicio
datos_ejercicio <- generar_datos()
```

```{r sistema_distractores_avanzado, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")

# SISTEMA AVANZADO DE DISTRACTORES para competencia Formulación-Ejecución
# 30% probabilidad de valores duplicados con justificaciones diferentes
permitir_valores_duplicados <- sample(c(TRUE, FALSE), 1, prob = c(0.3, 0.7))

# Extraer datos necesarios
datos_ord <- datos_ejercicio$datos_ordenados
mediana_correcta <- datos_ejercicio$mediana_correcta
n <- length(datos_ord)

# GENERAR 8+ TIPOS DIFERENTES DE DISTRACTORES
afirmaciones_incorrectas <- c()

# DISTRACTOR 1: Confundir mediana con media aritmética
media_calculada <- round(mean(datos_ord), 1)
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("La mediana es ", media_calculada, " porque se calcula sumando todos los valores y dividiendo por el número de datos"))

# DISTRACTOR 2: Confundir mediana con moda (valor más frecuente)
# Como evitamos duplicados, usamos el primer valor como "supuesta moda"
moda_incorrecta <- datos_ord[1]
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("La mediana es ", moda_incorrecta, " porque es el valor que más se repite en el conjunto de datos"))

# DISTRACTOR 3: Error en ordenamiento - usar dato sin ordenar
dato_desordenado <- sample(datos_ejercicio$asistencia, 1)
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("La mediana es ", dato_desordenado, " porque es el valor central sin necesidad de ordenar los datos"))

# DISTRACTOR 4: Confundir con valor máximo
valor_maximo <- max(datos_ord)
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("La mediana es ", valor_maximo, " porque representa el valor más alto del conjunto"))

# DISTRACTOR 5: Confundir con valor mínimo
valor_minimo <- min(datos_ord)
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("La mediana es ", valor_minimo, " porque es el punto de partida para el cálculo"))

# DISTRACTOR 6: Error en posición central (usar n/2 en lugar de (n+1)/2)
if (n %% 2 == 1) {
  posicion_incorrecta <- datos_ord[n %/% 2]
} else {
  posicion_incorrecta <- datos_ord[n %/% 2]
}
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("La mediana es ", posicion_incorrecta, " porque está en la posición n/2 del conjunto ordenado"))

# DISTRACTOR 7: Sumar en lugar de promediar (para datos pares)
if (datos_ejercicio$es_par) {
  pos_central1 <- n / 2
  pos_central2 <- pos_central1 + 1
  suma_centrales <- datos_ord[pos_central1] + datos_ord[pos_central2]
  afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
    paste0("La mediana es ", suma_centrales, " porque es la suma de los dos valores centrales"))
}

# DISTRACTOR 8: Rango (diferencia entre máximo y mínimo)
rango_datos <- valor_maximo - valor_minimo
afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
  paste0("La mediana es ", rango_datos, " porque representa la diferencia entre el valor máximo y mínimo"))

# JUSTIFICACIONES ALTERNATIVAS para el valor correcto (pero con razonamiento incorrecto)
justificaciones_incorrectas_valor_correcto <- c(
  paste0("La mediana es ", mediana_correcta, " porque representa el punto medio del rango de datos"),
  paste0("La mediana es ", mediana_correcta, " porque es el valor que mejor representa el conjunto"),
  paste0("La mediana es ", mediana_correcta, " porque se obtiene al aplicar la fórmula (máximo + mínimo) / 2"),
  paste0("La mediana es ", mediana_correcta, " porque es el promedio de todos los valores del conjunto")
)

# AFIRMACIÓN CORRECTA
if (datos_ejercicio$es_par) {
  pos_central1 <- n / 2
  pos_central2 <- pos_central1 + 1
  afirmacion_correcta <- paste0("La mediana es ", mediana_correcta, 
    " porque es el promedio de los dos valores centrales (", 
    datos_ord[pos_central1], " y ", datos_ord[pos_central2], 
    ") en el conjunto ordenado")
} else {
  pos_central <- (n + 1) / 2
  afirmacion_correcta <- paste0("La mediana es ", mediana_correcta, 
    " porque es el valor central (posición ", pos_central, 
    ") en el conjunto de datos ordenados")
}

# LÓGICA DE SELECCIÓN ADAPTADA
if (permitir_valores_duplicados) {
  # 30% casos: Incluir 1 justificación incorrecta para el valor correcto + 2 valores diferentes
  justificacion_incorrecta_seleccionada <- sample(justificaciones_incorrectas_valor_correcto, 1)
  distractores_diferentes <- sample(afirmaciones_incorrectas, 2)
  
  todas_afirmaciones <- c(afirmacion_correcta, justificacion_incorrecta_seleccionada, distractores_diferentes)
} else {
  # 70% casos: Modo tradicional - todos los valores diferentes
  distractores_seleccionados <- sample(afirmaciones_incorrectas, 3)
  todas_afirmaciones <- c(afirmacion_correcta, distractores_seleccionados)
}

# Mezclar opciones aleatoriamente
opciones_mezcladas <- sample(todas_afirmaciones)
indice_correcto <- which(opciones_mezcladas == afirmacion_correcta)

# Crear vector de solución para r-exams
solucion <- rep(FALSE, 4)
solucion[indice_correcto] <- TRUE
```

```{r validaciones_matematicas, message=FALSE, warning=FALSE}
options(OutDec = ".")

# VALIDACIONES MATEMÁTICAS para mediana
test_that("Validaciones de rangos de asistencia", {
  expect_true(all(datos_ejercicio$asistencia >= 200))
  expect_true(all(datos_ejercicio$asistencia <= 600))
})

test_that("Coherencia entre número de salas y datos", {
  expect_equal(length(datos_ejercicio$asistencia), datos_ejercicio$num_salas)
  expect_equal(length(datos_ejercicio$letras_salas), datos_ejercicio$num_salas)
})

test_that("Cálculo correcto de mediana", {
  # Verificar cálculo manual de mediana
  datos_ord_test <- sort(datos_ejercicio$asistencia)
  n_test <- length(datos_ord_test)

  if (n_test %% 2 == 1) {
    mediana_esperada <- datos_ord_test[(n_test + 1) / 2]
  } else {
    pos1 <- n_test / 2
    pos2 <- pos1 + 1
    mediana_esperada <- (datos_ord_test[pos1] + datos_ord_test[pos2]) / 2
  }

  expect_equal(datos_ejercicio$mediana_correcta, mediana_esperada)
})

test_that("Sistema de distractores funcional", {
  expect_equal(length(opciones_mezcladas), 4)
  expect_true(afirmacion_correcta %in% opciones_mezcladas)
  expect_equal(length(unique(opciones_mezcladas)), 4)  # Todas las opciones textualmente únicas
})
```

```{r crear_tabla_tikz, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")

# Crear tabla de datos usando TikZ para mejor presentación
datos_tabla <- data.frame(
  Sala = datos_ejercicio$letras_salas,
  Asistencia = datos_ejercicio$asistencia
)

# Organizar en dos columnas si hay muchas salas
if (nrow(datos_tabla) > 8) {
  # Dividir en dos columnas
  mitad <- ceiling(nrow(datos_tabla) / 2)
  col1 <- datos_tabla[1:mitad, ]
  col2_indices <- (mitad + 1):nrow(datos_tabla)

  if (length(col2_indices) > 0) {
    col2 <- datos_tabla[col2_indices, ]
    # Completar con filas vacías si es necesario
    while (nrow(col2) < nrow(col1)) {
      col2 <- rbind(col2, data.frame(Sala = "", Asistencia = ""))
    }
  } else {
    col2 <- data.frame(Sala = rep("", nrow(col1)), Asistencia = rep("", nrow(col1)))
  }

  tabla_tikz <- c(
    "\\begin{tikzpicture}",
    "\\node[inner sep=0pt] {",
    "  \\begin{tabular}{|c|c|c|c|}",
    "    \\hline",
    "    \\textbf{Sala} & \\textbf{Asistencia} & \\textbf{Sala} & \\textbf{Asistencia} \\\\",
    "    \\hline"
  )

  for (i in 1:nrow(col1)) {
    if (col2$Sala[i] != "") {
      tabla_tikz <- c(tabla_tikz,
        paste0("    ", col1$Sala[i], " & ", col1$Asistencia[i], " & ",
               col2$Sala[i], " & ", col2$Asistencia[i], " \\\\"))
    } else {
      tabla_tikz <- c(tabla_tikz,
        paste0("    ", col1$Sala[i], " & ", col1$Asistencia[i], " & & \\\\"))
    }
    tabla_tikz <- c(tabla_tikz, "    \\hline")
  }
} else {
  # Una sola columna
  tabla_tikz <- c(
    "\\begin{tikzpicture}",
    "\\node[inner sep=0pt] {",
    "  \\begin{tabular}{|c|c|}",
    "    \\hline",
    "    \\textbf{Sala} & \\textbf{Asistencia} \\\\",
    "    \\hline"
  )

  for (i in 1:nrow(datos_tabla)) {
    tabla_tikz <- c(tabla_tikz,
      paste0("    ", datos_tabla$Sala[i], " & ", datos_tabla$Asistencia[i], " \\\\"))
    tabla_tikz <- c(tabla_tikz, "    \\hline")
  }
}

tabla_tikz <- c(tabla_tikz,
  "  \\end{tabular}",
  "};",
  "\\end{tikzpicture}"
)
```

```{r grafico_python, message=FALSE, warning=FALSE}
options(OutDec = ".")

# Crear histograma de distribución de asistencia usando Python/matplotlib
codigo_histograma <- paste0("
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np

plt.style.use('default')
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.2

# Datos de asistencia
asistencia = [", paste(datos_ejercicio$asistencia, collapse = ", "), "]

fig, ax = plt.subplots(1, 1, figsize=(8, 6))

# Crear histograma
n_bins = min(8, len(asistencia) // 2 + 1)
ax.hist(asistencia, bins=n_bins, color='#2E86AB', alpha=0.7, edgecolor='black', linewidth=1.2)

# Añadir línea vertical para la mediana
ax.axvline(x=", datos_ejercicio$mediana_correcta, ", color='red', linestyle='--', linewidth=2,
           label='Mediana = ", datos_ejercicio$mediana_correcta, "')

ax.set_xlabel('Número de Asistentes', fontsize=12)
ax.set_ylabel('Frecuencia', fontsize=12)
ax.set_title('Distribución de Asistencia en las Salas de ", datos_ejercicio$contexto$nombre_cine, "', fontsize=14)
ax.grid(True, alpha=0.3)
ax.legend()

plt.tight_layout()
plt.savefig('histograma_asistencia.png', dpi=150, bbox_inches='tight',
           facecolor='white', edgecolor='none')
plt.savefig('histograma_asistencia.pdf', dpi=150, bbox_inches='tight',
           facecolor='white', edgecolor='none')
plt.close()
")

py_run_string(codigo_histograma)
```

Question
========

El centro comercial `r datos_ejercicio$contexto$nombre_cine` registró la asistencia de espectadores durante una semana en sus diferentes `r datos_ejercicio$contexto$tipo_sala` que proyectaban películas de `r datos_ejercicio$contexto$genero`. Los datos se muestran en la siguiente tabla:

```{r mostrar_tabla, echo=FALSE, results='asis'}
# Detectar formato de salida
formatos_moodle <- c("exams2moodle", "exams2qti12", "exams2qti21", "exams2openolat")
es_moodle <- (match_exams_call() %in% formatos_moodle)

if (es_moodle) {
  # Para Moodle, usar tabla HTML simple
  if (nrow(datos_tabla) > 8) {
    # Tabla de dos columnas para Moodle
    cat("<table border='1' style='border-collapse: collapse; margin: 0 auto;'>")
    cat("<tr><th style='padding: 8px; text-align: center; background-color: #f0f0f0;'>Sala</th>")
    cat("<th style='padding: 8px; text-align: center; background-color: #f0f0f0;'>Asistencia</th>")
    cat("<th style='padding: 8px; text-align: center; background-color: #f0f0f0;'>Sala</th>")
    cat("<th style='padding: 8px; text-align: center; background-color: #f0f0f0;'>Asistencia</th></tr>")

    mitad <- ceiling(nrow(datos_tabla) / 2)
    for (i in 1:mitad) {
      cat("<tr>")
      cat("<td style='padding: 8px; text-align: center;'>", datos_tabla$Sala[i], "</td>")
      cat("<td style='padding: 8px; text-align: center;'>", datos_tabla$Asistencia[i], "</td>")

      if (i + mitad <= nrow(datos_tabla)) {
        cat("<td style='padding: 8px; text-align: center;'>", datos_tabla$Sala[i + mitad], "</td>")
        cat("<td style='padding: 8px; text-align: center;'>", datos_tabla$Asistencia[i + mitad], "</td>")
      } else {
        cat("<td style='padding: 8px; text-align: center;'></td>")
        cat("<td style='padding: 8px; text-align: center;'></td>")
      }
      cat("</tr>")
    }
    cat("</table>")
  } else {
    # Tabla simple para Moodle
    cat("<table border='1' style='border-collapse: collapse; margin: 0 auto;'>")
    cat("<tr><th style='padding: 8px; text-align: center; background-color: #f0f0f0;'>Sala</th>")
    cat("<th style='padding: 8px; text-align: center; background-color: #f0f0f0;'>Asistencia</th></tr>")

    for (i in 1:nrow(datos_tabla)) {
      cat("<tr>")
      cat("<td style='padding: 8px; text-align: center;'>", datos_tabla$Sala[i], "</td>")
      cat("<td style='padding: 8px; text-align: center;'>", datos_tabla$Asistencia[i], "</td>")
      cat("</tr>")
    }
    cat("</table>")
  }
} else {
  # Para PDF/Word, usar TikZ
  include_tikz(tabla_tikz,
               name = "tabla_asistencia",
               markup = "markdown",
               format = typ,
               packages = c("tikz", "colortbl", "xcolor"),
               width = "10cm")
}
```

Para analizar la tendencia central de asistencia y tomar decisiones sobre la programación de películas, el gerente necesita calcular la mediana de asistencia.

¿Cuál de las siguientes afirmaciones explica correctamente cómo calcular la mediana de asistencia para las `r datos_ejercicio$num_salas` salas de cine?

**Nota:** La mediana es la medida de tendencia central que divide el conjunto de datos ordenados en dos mitades iguales.

Answerlist
----------
- `r opciones_mezcladas[1]`
- `r opciones_mezcladas[2]`
- `r opciones_mezcladas[3]`
- `r opciones_mezcladas[4]`

Solution
========

Para resolver este problema de **Formulación-Ejecución**, debemos aplicar el concepto de mediana y seguir un proceso sistemático de cálculo.

### Paso 1: Comprender el concepto de mediana

La **mediana** es el valor que ocupa la posición central cuando los datos están ordenados de menor a mayor. Es una medida de tendencia central que divide el conjunto de datos en dos mitades iguales.

### Paso 2: Organizar los datos

**Datos de asistencia (sin ordenar):**

`r paste(datos_ejercicio$asistencia, collapse = ", ")`

**Datos ordenados de menor a mayor:**

`r paste(datos_ejercicio$datos_ordenados, collapse = ", ")`

**Total de salas:** n = `r datos_ejercicio$num_salas`

### Paso 3: Aplicar la fórmula de mediana según el número de datos

```{r explicacion_calculo, echo=FALSE, results='asis'}
n <- datos_ejercicio$num_salas
datos_ord <- datos_ejercicio$datos_ordenados

if (datos_ejercicio$es_par) {
  cat("Como tenemos un **número par** de datos (n =", n, "), la mediana se calcula como el **promedio de los dos valores centrales**.\n\n")

  pos_central1 <- n / 2
  pos_central2 <- pos_central1 + 1

  cat("- **Posición del primer valor central:** n/2 =", n, "/2 =", pos_central1, "\n")
  cat("- **Posición del segundo valor central:** (n/2) + 1 =", pos_central1, "+ 1 =", pos_central2, "\n\n")

  cat("- **Primer valor central:** posición", pos_central1, "=", datos_ord[pos_central1], "\n")
  cat("- **Segundo valor central:** posición", pos_central2, "=", datos_ord[pos_central2], "\n\n")

  cat("**Cálculo de la mediana:**\n")
  cat("Mediana = (", datos_ord[pos_central1], " + ", datos_ord[pos_central2], ") ÷ 2 = ",
      (datos_ord[pos_central1] + datos_ord[pos_central2]), " ÷ 2 = **", datos_ejercicio$mediana_correcta, "**\n\n")
} else {
  cat("Como tenemos un **número impar** de datos (n =", n, "), la mediana es el **valor central**.\n\n")

  pos_central <- (n + 1) / 2

  cat("- **Posición del valor central:** (n + 1)/2 = (", n, " + 1)/2 =", pos_central, "\n\n")

  cat("**Mediana:** posición", pos_central, "=", datos_ord[pos_central], "\n\n")
}
```

### Paso 4: Verificación del resultado

La mediana calculada es **`r datos_ejercicio$mediana_correcta`**, lo que significa que:

- El 50% de las salas tuvieron una asistencia menor o igual a `r datos_ejercicio$mediana_correcta`
- El 50% de las salas tuvieron una asistencia mayor o igual a `r datos_ejercicio$mediana_correcta`

### Paso 5: Análisis de las opciones

```{r analisis_opciones, echo=FALSE, results='asis'}
cat("**Respuesta correcta:** ", afirmacion_correcta, "\n\n")

cat("**¿Por qué las otras opciones son incorrectas?**\n\n")

for (i in 1:4) {
  if (opciones_mezcladas[i] != afirmacion_correcta) {
    opcion_letra <- LETTERS[i]
    cat("- **Opción", opcion_letra, ":** ", opciones_mezcladas[i], "\n")

    # Identificar el tipo de error
    if (grepl("sumando todos los valores", opciones_mezcladas[i])) {
      cat("  *Error conceptual: Confunde mediana con media aritmética.*\n\n")
    } else if (grepl("más se repite", opciones_mezcladas[i])) {
      cat("  *Error conceptual: Confunde mediana con moda.*\n\n")
    } else if (grepl("sin necesidad de ordenar", opciones_mezcladas[i])) {
      cat("  *Error procedimental: No comprende que los datos deben ordenarse.*\n\n")
    } else if (grepl("valor más alto", opciones_mezcladas[i])) {
      cat("  *Error conceptual: Confunde mediana con valor máximo.*\n\n")
    } else if (grepl("punto de partida", opciones_mezcladas[i])) {
      cat("  *Error conceptual: Confunde mediana with valor mínimo.*\n\n")
    } else if (grepl("suma de los dos valores centrales", opciones_mezcladas[i])) {
      cat("  *Error procedimental: Suma en lugar de promediar los valores centrales.*\n\n")
    } else if (grepl("punto medio del rango", opciones_mezcladas[i]) ||
               grepl("mejor representa", opciones_mezcladas[i]) ||
               grepl("fórmula básica", opciones_mezcladas[i])) {
      cat("  *Error conceptual: Justificación incorrecta para el valor correcto.*\n\n")
    } else {
      cat("  *Error en el procedimiento de cálculo.*\n\n")
    }
  }
}
```

### Conclusión

La competencia de **Formulación-Ejecución** requiere que identifiquemos la estrategia correcta (ordenar datos y aplicar la fórmula de mediana) y la ejecutemos paso a paso para obtener el resultado correcto: **`r datos_ejercicio$mediana_correcta`**.

Answerlist
----------
- `r if(solucion[1]) "Verdadero" else "Falso"`
- `r if(solucion[2]) "Verdadero" else "Falso"`
- `r if(solucion[3]) "Verdadero" else "Falso"`
- `r if(solucion[4]) "Verdadero" else "Falso"`

Meta-information
================
exname: mediana_salas_cine_formulacion_ejecucion
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Estadística|Medidas de tendencia central|Mediana|Formulación y ejecución
exextra[Type]: Formulación y ejecución
exextra[Level]: 2
exextra[Language]: es
exextra[Course]: Matemáticas ICFES

```{r prueba_diversidad, include=FALSE}
# PRUEBA DE DIVERSIDAD - Verificar mínimo 300 versiones únicas
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    datos_test <- generar_datos()
    # Crear hash único basado en contexto, datos y mediana
    hash_datos <- digest::digest(list(
      contexto = datos_test$contexto,
      asistencia = datos_test$asistencia,
      mediana = datos_test$mediana_correcta
    ))
    versiones[[i]] <- hash_datos
  }

  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones únicas. Se requieren al menos 300."))

  cat("✅ Prueba de diversidad exitosa:", n_versiones_unicas, "versiones únicas generadas\n")
})

# PRUEBA ESPECÍFICA DEL SISTEMA AVANZADO DE DISTRACTORES
test_that("Prueba del sistema avanzado de distractores", {
  for(i in 1:50) {
    datos_test <- generar_datos()

    # Simular generación de distractores para esta prueba
    set.seed(i)  # Semilla fija para reproducibilidad en la prueba

    # Verificar que se pueden generar 4 opciones únicas
    expect_true(length(datos_test$asistencia) >= 5,
               info = "Debe haber al menos 5 salas para generar suficientes distractores")

    # Verificar cálculo correcto de mediana
    datos_ord_test <- sort(datos_test$asistencia)
    n_test <- length(datos_ord_test)

    if (n_test %% 2 == 1) {
      mediana_esperada <- datos_ord_test[(n_test + 1) / 2]
    } else {
      pos1 <- n_test / 2
      pos2 <- pos1 + 1
      mediana_esperada <- (datos_ord_test[pos1] + datos_ord_test[pos2]) / 2
    }

    expect_equal(datos_test$mediana_correcta, mediana_esperada,
                info = paste("Error en cálculo de mediana para iteración", i))
  }

  cat("✅ Sistema de distractores validado correctamente\n")
})
```
